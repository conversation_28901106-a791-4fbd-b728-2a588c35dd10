<?php

namespace App\Http\Controllers\MainControllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\ClientRequests\ClientDevicesRequest;
use App\Http\Requests\ClientRequests\ClientSessionsRequest;
use App\Http\Requests\ClientRequests\IndexClientRequest;
use App\Http\Requests\ClientRequests\StoreClientRequest;
use App\Http\Requests\ClientRequests\UpdateClientRequest;
use App\Http\Resources\ClientResource;
use App\Http\Resources\DeviceResource;
use App\Http\Resources\SessionResource;
use App\Models\Client;
use App\Models\Device;
use App\Models\Sanctum\PersonalAccessToken;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Str;

class ClientController extends Controller
{

    function __construct()
    {
        $this->middleware('permission:clients', ['only' => ['index']]);
        $this->middleware('permission:clients/create', ['only' => ['store']]);
        $this->middleware('permission:clients/update', ['only' => ['update']]);
        $this->middleware('permission:clients/details', ['only' => ['show']]);
        $this->middleware('permission:clients/devices', ['only' => ['devices']]);
        $this->middleware('permission:clients/sessions', ['only' => ['sessions']]);
        $this->middleware('permission:clients/delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(IndexClientRequest $request)
    {
        $parts = explode(':', $request->sort);
        $column = $request->sort ? $parts[0] : 'id';
        $direction = $request->sort ? $parts[1] : 'desc';
        $query = Client::search($request->search)
        ->orderBy($column, $direction)
        ->query(function ($subQuery){
            $subQuery->with('lastAccessToken');
        });
        return ClientResource::collection($query->paginate($request->limit, 'page', $request->page));
    }

    /**
     * @param StoreClientRequest $request
     * @return false|string
     */
    public function store(StoreClientRequest $request)
    {
        $client = Client::create($request->validated());
        $client->update(['username' => $client->id . Str::random(3) ]);
        return self::jsonResponse('success', $client);
    }

    /***
     * @param Client $client
     * @return false|string
     */
    public function show(Client $client)
    {
        $client->load('lastAccessToken');
        return self::jsonResponse('success', $client);
    }

    /**
     * @param UpdateClientRequest $request
     * @param Client $client
     * @return false|string
     */
    public function update(UpdateClientRequest $request, Client $client)
    {
        $client->update($request->validated());
        return self::jsonResponse('success', $client->refresh());
    }

    /***
     * @param Client $client
     * @return false|string
     */
    public function destroy(Client $client)
    {
        if($client->hasRelations())
        throw ValidationException::withMessages(['id' => trans('validation.has_relations')]);

        $client->delete();
        return self::jsonResponse('success');
    }

    public function devices(ClientDevicesRequest $request)
    {
        $parts = explode(':', $request->sort);
        $column = $request->sort ? $parts[0] : 'id';
        $direction = $request->sort ? $parts[1] : 'desc';
        $devices = Device::search($request->search)
        ->orderBy($column, $direction)
        ->where('client_id', $request->id)
        ->paginate($request->limit, 'page', $request->page);
        return DeviceResource::collection($devices);
    }

    public function sessions(ClientSessionsRequest $request)
    {
        $query = PersonalAccessToken::search($request->search)
        ->query(function($query) use ($request) {
            $query->with('country', 'device');
            $query->where('tokenable_type', Client::class);
            $query->where('tokenable_id', $request->id);

            if ($request->search) {

                // Search in country name if relation exists
                $query->orWhereHas('country', function($subquery) use ($request) {
                    $subquery->where('name', 'like', '%' . $request->search . '%');
                });

                // Search in device_os field
                $query->orWhereHas('device', function($subquery) use ($request) {
                    $subquery->where('device_os', 'like', '%' . $request->search . '%');
                    $subquery->orWhere('device_id', 'like', '%' . $request->search . '%');
                });
            }

            $query = $this->orderBy($query, $request, 'personal_access_tokens');
        });

        return SessionResource::collection($query->paginate($request->limit, 'page', $request->page));
    }
}
