openapi: 3.0.3
info:
  title: 'Laravel-vue-base API Documentation'
  description: ''
  version: 1.0.0
servers:
  -
    url: 'http://captainvpn.test'
tags:
  -
    name: Endpoints
    description: ''
components:
  securitySchemes:
    default:
      type: http
      scheme: bearer
      description: 'You can retrieve your token by visiting your dashboard and clicking <b>Generate API token</b>.'
security:
  -
    default: []
paths:
  /client/login:
    post:
      summary: ''
      operationId: postClientLogin
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                device_id:
                  type: string
                  description: 'Must not be greater than 100 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
                password:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
              required:
                - device_id
                - email
                - password
  /client/login-as-guest:
    post:
      summary: ''
      operationId: postClientLoginAsGuest
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                device_id:
                  type: string
                  description: 'Must not be greater than 100 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
              required:
                - device_id
  /client/register:
    post:
      summary: ''
      operationId: postClientRegister
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
                password:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
              required:
                - email
                - password
  /client/resend-verification-code:
    post:
      summary: ''
      operationId: postClientResendVerificationCode
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
              required:
                - email
  /client/verify-email:
    post:
      summary: ''
      operationId: postClientVerifyEmail
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                device_id:
                  type: string
                  description: 'Must not be greater than 100 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
                email:
                  type: string
                  description: 'Must be a valid email address. The <code>NULL</code> of an existing record in the clients table.'
                  example: <EMAIL>
                  nullable: false
                verification_code:
                  type: string
                  description: 'Must be 6 digits.'
                  example: '810798'
                  nullable: false
              required:
                - device_id
                - email
                - verification_code
  /client/send-reset-password-code:
    post:
      summary: ''
      operationId: postClientSendResetPasswordCode
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 'Must be a valid email address. The <code>NULL</code> of an existing record in the clients table.'
                  example: <EMAIL>
                  nullable: false
              required:
                - email
  /client/verify-reset-code-login:
    post:
      summary: ''
      operationId: postClientVerifyResetCodeLogin
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                device_id:
                  type: string
                  description: 'Must not be greater than 100 characters.'
                  example: vmqeopfuudtdsufvyvddq
                  nullable: false
                email:
                  type: string
                  description: 'Must be a valid email address. The <code>NULL</code> of an existing record in the clients table.'
                  example: <EMAIL>
                  nullable: false
                verification_code:
                  type: string
                  description: 'Must be 6 digits.'
                  example: '810798'
                  nullable: false
              required:
                - device_id
                - email
                - verification_code
  /client/social-login:
    post:
      summary: ''
      operationId: postClientSocialLogin
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                token:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
                provider:
                  type: string
                  description: ''
                  example: apple
                  nullable: false
                  enum:
                    - google
                    - apple
                device_id:
                  type: string
                  description: 'Must not be greater than 100 characters.'
                  example: mqeopfuudtdsufvyvddqa
                  nullable: false
              required:
                - token
                - provider
                - device_id
  /client/settings:
    get:
      summary: ''
      operationId: getClientSettings
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: 'Operation done successfully.'
                  data:
                    -
                      name: 'Email Address'
                      code: email
                      value: <EMAIL>
                      type: email
                    -
                      name: 'Max Connection Time'
                      code: conn_limit
                      value: '180'
                      type: numeric
                    -
                      name: 'Ads Activation'
                      code: ads_act
                      value: '0'
                      type: bool
                    -
                      name: 'Upgrade To Premium'
                      code: premium
                      value: '0'
                      type: bool
                    -
                      name: 'Server RAM threshold (%)'
                      code: serv_ram_t
                      value: '80'
                      type: numeric
                    -
                      name: 'Server CPU threshold (%)'
                      code: serv_cpu_t
                      value: '80'
                      type: numeric
                    -
                      name: 'Server DISK threshold (%)'
                      code: serv_dis_t
                      value: '80'
                      type: numeric
                    -
                      name: Phone1
                      code: pho1
                      value: +971-4354-332
                      type: text
                    -
                      name: facebook
                      code: acc_fa
                      value: 'https://www.facebook.com/CaptainVpn'
                      type: link
                    -
                      name: instagram
                      code: acc_in
                      value: 'https://www.instagram.com/CaptainVpn'
                      type: link
                    -
                      name: X
                      code: acc_x
                      value: 'https://www.x.com/CaptainVpn'
                      type: link
                    -
                      name: Youtube
                      code: acc_yo
                      value: 'https://www.Youtube.com/CaptainVpn'
                      type: link
                    -
                      name: 'About Us'
                      code: abo_us
                      value: 'Captain Vpn is ....'
                      type: text
                    -
                      name: 'privacy policy'
                      code: pr_po
                      value: 'We are committed to maintaining the accuracy, confidentiality, and security of your personally identifiable information ("Personal Information")....'
                      type: text
                    -
                      name: 'Terms of Service'
                      code: ter_s
                      value: '<p><strong>Terms of Service</strong></p><p><em>Last Updated: April 22, 2025</em></p><p><br></p><p>These Terms of Service (“Terms”), along with the Privacy Policy, govern your use of the services, website, applications, content, and software (collectively, the “Services”) offered by CaptainVPN (“CaptainVPN,” “we,” “us,” or “our”), a company incorporated in the United States.</p><p>By using the Services, you agree to these Terms and the Privacy Policy (together, the “Agreement”). This Agreement is a legally binding contract between you and CaptainVPN. If you disagree with any part of this Agreement, you may not use the Services.</p><p><br></p><h3>Acceptance</h3><p>By accessing or using the Services, you represent that:</p><ul><li>You are at least 18 years old (or the age of majority in your jurisdiction);</li><li>You have read, understood, and agree to be bound by this Agreement;</li><li>All registration information you provide is accurate and complete.</li></ul><p>If you accept these Terms on behalf of an employer or entity, you warrant that you have full legal authority to bind them to this Agreement.</p><p><br></p><h3>Modification</h3><p>CaptainVPN may update these Terms at any time. Material changes (e.g., pricing, dispute resolution, or data practices) will be communicated via email, Account notifications, or Site banners. Continued use after changes take effect constitutes acceptance.</p><p><br></p><h3>Privacy Policy</h3><p>CaptainVPN does not log browsing history, traffic destination, data content, DNS queries, or user-linked IP addresses, except as necessary for billing, troubleshooting, or legal compliance. For details, review our Privacy Policy, which complies with U.S. laws (e.g., CCPA) and international regulations (e.g., GDPR for EU users).</p><p><br></p><h3>Subscriptions</h3><ul><li>Subscription fees and plans are listed on the Site. CaptainVPN may change fees with 30 days'' notice (effective upon renewal).</li><li><strong>Auto-renewal</strong>: Enabled by default unless canceled via your Account or by emailing <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><EMAIL></a>. California users: You may cancel auto-renewal online at least 30 days before renewal.</li><li>You are solely responsible for Account security and activity.</li><li><br></li></ul><h3>Refund Policy</h3><ul><li><strong>Money-Back Guarantee</strong>: Full refunds within 3 days of purchase.</li><li>Post-3-day refunds require proof of Service unavailability and are issued at CaptainVPN’s discretion.</li><li>Refunds are processed in USD; amounts may vary due to exchange rates or third-party fees.</li><li>In-app purchases: Refunds via Apple/Google stores only.</li><li><br></li></ul><h3>Acceptable Use Policy</h3><p>You agree not to:</p><ul><li>Engage in illegal activities (fraud, hacking, terrorism, phishing) under U.S. federal or state laws.</li><li>Transmit viruses, malware, or spam.</li><li>Share copyrighted materials (see DMCA Compliance below) or child exploitation content.</li><li>Harass others or conduct DDoS attacks/unauthorized scanning.</li><li>Use unauthorized payment methods.</li></ul><p>Violations may result in immediate termination without refund.</p><p><br></p><h3>Third-Party Content</h3><p>CaptainVPN is not responsible for third-party websites, content, or services linked through the Services. Use at your own risk.</p><p><br></p><h3>Disclaimers</h3><p>Services are provided “as-is.” CaptainVPN disclaims all warranties (express/implied), including merchantability or fitness for a particular purpose. Service availability, speed, and coverage may vary.</p><p><br></p><h3>Limitations of Liability</h3><p>CaptainVPN is not liable for:</p><ul><li>Service interruptions;</li><li>Third-party actions;</li><li>Unauthorized Account access;</li><li>Indirect/consequential damages.</li></ul><p>Exclusions do not apply to liability under California law or federal statutes (e.g., death, personal injury, fraud).</p><p><br></p><h3>Indemnification</h3><p>You agree to indemnify CaptainVPN for claims arising from your breach of this Agreement or misuse of the Services, including reasonable attorneys'' fees.</p><p><br></p><h3>Termination</h3><ul><li><strong>By You</strong>: Cancel via Account dashboard (no prorated refunds).</li><li><strong>By CaptainVPN</strong>: Immediate termination for violations, with no refund.</li></ul><p><br></p><h3>Dispute Resolution &amp; Governing Law</h3><ul><li><strong>Governing Law</strong>: This Agreement is governed by the laws of the State of Delaware without regard to conflict-of-law principles.</li><li><strong>Arbitration</strong>: Any dispute will be resolved by binding arbitration under the AAA Commercial Rules. Class actions are waived.</li></ul><p><br></p><h3>General</h3><ul><li><strong>Force Majeure</strong>: CaptainVPN is not liable for delays beyond its control (e.g., natural disasters, government actions).</li><li><strong>Assignment</strong>: CaptainVPN may transfer this Agreement to a successor entity.</li><li><strong>Severability</strong>: Invalid clauses will be amended to reflect intent; remainder of Agreement remains valid.</li></ul><p><br></p><p><strong>Contact Us:</strong></p><p>For questions, contact <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><EMAIL></a>.</p>'
                      type: text
                    -
                      name: 'Video Ad Activation'
                      code: vid_ad_act
                      value: '0'
                      type: bool
                    -
                      name: 'Main Load Ad Activation'
                      code: ml_ad_act
                      value: '0'
                      type: bool
                    -
                      name: 'All Ads Activation'
                      code: all_ad_act
                      value: '0'
                      type: bool
                    -
                      name: 'Golden Hour Ad Period'
                      code: gh_ad_per
                      value: '60'
                      type: numeric
                    -
                      name: 'One Golden Hour Activation'
                      code: one_gh_act
                      value: '0'
                      type: bool
                    -
                      name: 'Protocol Input Name'
                      code: prot_name
                      value: 'select mode'
                      type: text
                    -
                      name: 'Server Input Name'
                      code: serv_name
                      value: 'select country'
                      type: text
                    -
                      name: 'Banner Ad Activation'
                      code: ban_ad_act
                      value: '0'
                      type: bool
                properties:
                  message:
                    type: string
                    example: 'Operation done successfully.'
                  data:
                    type: array
                    example:
                      -
                        name: 'Email Address'
                        code: email
                        value: <EMAIL>
                        type: email
                      -
                        name: 'Max Connection Time'
                        code: conn_limit
                        value: '180'
                        type: numeric
                      -
                        name: 'Ads Activation'
                        code: ads_act
                        value: '0'
                        type: bool
                      -
                        name: 'Upgrade To Premium'
                        code: premium
                        value: '0'
                        type: bool
                      -
                        name: 'Server RAM threshold (%)'
                        code: serv_ram_t
                        value: '80'
                        type: numeric
                      -
                        name: 'Server CPU threshold (%)'
                        code: serv_cpu_t
                        value: '80'
                        type: numeric
                      -
                        name: 'Server DISK threshold (%)'
                        code: serv_dis_t
                        value: '80'
                        type: numeric
                      -
                        name: Phone1
                        code: pho1
                        value: +971-4354-332
                        type: text
                      -
                        name: facebook
                        code: acc_fa
                        value: 'https://www.facebook.com/CaptainVpn'
                        type: link
                      -
                        name: instagram
                        code: acc_in
                        value: 'https://www.instagram.com/CaptainVpn'
                        type: link
                      -
                        name: X
                        code: acc_x
                        value: 'https://www.x.com/CaptainVpn'
                        type: link
                      -
                        name: Youtube
                        code: acc_yo
                        value: 'https://www.Youtube.com/CaptainVpn'
                        type: link
                      -
                        name: 'About Us'
                        code: abo_us
                        value: 'Captain Vpn is ....'
                        type: text
                      -
                        name: 'privacy policy'
                        code: pr_po
                        value: 'We are committed to maintaining the accuracy, confidentiality, and security of your personally identifiable information ("Personal Information")....'
                        type: text
                      -
                        name: 'Terms of Service'
                        code: ter_s
                        value: '<p><strong>Terms of Service</strong></p><p><em>Last Updated: April 22, 2025</em></p><p><br></p><p>These Terms of Service (“Terms”), along with the Privacy Policy, govern your use of the services, website, applications, content, and software (collectively, the “Services”) offered by CaptainVPN (“CaptainVPN,” “we,” “us,” or “our”), a company incorporated in the United States.</p><p>By using the Services, you agree to these Terms and the Privacy Policy (together, the “Agreement”). This Agreement is a legally binding contract between you and CaptainVPN. If you disagree with any part of this Agreement, you may not use the Services.</p><p><br></p><h3>Acceptance</h3><p>By accessing or using the Services, you represent that:</p><ul><li>You are at least 18 years old (or the age of majority in your jurisdiction);</li><li>You have read, understood, and agree to be bound by this Agreement;</li><li>All registration information you provide is accurate and complete.</li></ul><p>If you accept these Terms on behalf of an employer or entity, you warrant that you have full legal authority to bind them to this Agreement.</p><p><br></p><h3>Modification</h3><p>CaptainVPN may update these Terms at any time. Material changes (e.g., pricing, dispute resolution, or data practices) will be communicated via email, Account notifications, or Site banners. Continued use after changes take effect constitutes acceptance.</p><p><br></p><h3>Privacy Policy</h3><p>CaptainVPN does not log browsing history, traffic destination, data content, DNS queries, or user-linked IP addresses, except as necessary for billing, troubleshooting, or legal compliance. For details, review our Privacy Policy, which complies with U.S. laws (e.g., CCPA) and international regulations (e.g., GDPR for EU users).</p><p><br></p><h3>Subscriptions</h3><ul><li>Subscription fees and plans are listed on the Site. CaptainVPN may change fees with 30 days'' notice (effective upon renewal).</li><li><strong>Auto-renewal</strong>: Enabled by default unless canceled via your Account or by emailing <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><EMAIL></a>. California users: You may cancel auto-renewal online at least 30 days before renewal.</li><li>You are solely responsible for Account security and activity.</li><li><br></li></ul><h3>Refund Policy</h3><ul><li><strong>Money-Back Guarantee</strong>: Full refunds within 3 days of purchase.</li><li>Post-3-day refunds require proof of Service unavailability and are issued at CaptainVPN’s discretion.</li><li>Refunds are processed in USD; amounts may vary due to exchange rates or third-party fees.</li><li>In-app purchases: Refunds via Apple/Google stores only.</li><li><br></li></ul><h3>Acceptable Use Policy</h3><p>You agree not to:</p><ul><li>Engage in illegal activities (fraud, hacking, terrorism, phishing) under U.S. federal or state laws.</li><li>Transmit viruses, malware, or spam.</li><li>Share copyrighted materials (see DMCA Compliance below) or child exploitation content.</li><li>Harass others or conduct DDoS attacks/unauthorized scanning.</li><li>Use unauthorized payment methods.</li></ul><p>Violations may result in immediate termination without refund.</p><p><br></p><h3>Third-Party Content</h3><p>CaptainVPN is not responsible for third-party websites, content, or services linked through the Services. Use at your own risk.</p><p><br></p><h3>Disclaimers</h3><p>Services are provided “as-is.” CaptainVPN disclaims all warranties (express/implied), including merchantability or fitness for a particular purpose. Service availability, speed, and coverage may vary.</p><p><br></p><h3>Limitations of Liability</h3><p>CaptainVPN is not liable for:</p><ul><li>Service interruptions;</li><li>Third-party actions;</li><li>Unauthorized Account access;</li><li>Indirect/consequential damages.</li></ul><p>Exclusions do not apply to liability under California law or federal statutes (e.g., death, personal injury, fraud).</p><p><br></p><h3>Indemnification</h3><p>You agree to indemnify CaptainVPN for claims arising from your breach of this Agreement or misuse of the Services, including reasonable attorneys'' fees.</p><p><br></p><h3>Termination</h3><ul><li><strong>By You</strong>: Cancel via Account dashboard (no prorated refunds).</li><li><strong>By CaptainVPN</strong>: Immediate termination for violations, with no refund.</li></ul><p><br></p><h3>Dispute Resolution &amp; Governing Law</h3><ul><li><strong>Governing Law</strong>: This Agreement is governed by the laws of the State of Delaware without regard to conflict-of-law principles.</li><li><strong>Arbitration</strong>: Any dispute will be resolved by binding arbitration under the AAA Commercial Rules. Class actions are waived.</li></ul><p><br></p><h3>General</h3><ul><li><strong>Force Majeure</strong>: CaptainVPN is not liable for delays beyond its control (e.g., natural disasters, government actions).</li><li><strong>Assignment</strong>: CaptainVPN may transfer this Agreement to a successor entity.</li><li><strong>Severability</strong>: Invalid clauses will be amended to reflect intent; remainder of Agreement remains valid.</li></ul><p><br></p><p><strong>Contact Us:</strong></p><p>For questions, contact <a href="mailto:<EMAIL>" rel="noopener noreferrer" target="_blank"><EMAIL></a>.</p>'
                        type: text
                      -
                        name: 'Video Ad Activation'
                        code: vid_ad_act
                        value: '0'
                        type: bool
                      -
                        name: 'Main Load Ad Activation'
                        code: ml_ad_act
                        value: '0'
                        type: bool
                      -
                        name: 'All Ads Activation'
                        code: all_ad_act
                        value: '0'
                        type: bool
                      -
                        name: 'Golden Hour Ad Period'
                        code: gh_ad_per
                        value: '60'
                        type: numeric
                      -
                        name: 'One Golden Hour Activation'
                        code: one_gh_act
                        value: '0'
                        type: bool
                      -
                        name: 'Protocol Input Name'
                        code: prot_name
                        value: 'select mode'
                        type: text
                      -
                        name: 'Server Input Name'
                        code: serv_name
                        value: 'select country'
                        type: text
                      -
                        name: 'Banner Ad Activation'
                        code: ban_ad_act
                        value: '0'
                        type: bool
                    items:
                      type: object
                      properties:
                        name:
                          type: string
                          example: 'Email Address'
                        code:
                          type: string
                          example: email
                        value:
                          type: string
                          example: <EMAIL>
                        type:
                          type: string
                          example: email
      tags:
        - Endpoints
  '/client/settings/{code}':
    get:
      summary: ''
      operationId: getClientSettingsCode
      description: ''
      parameters: []
      responses:
        200:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: 'Operation done successfully.'
                  data:
                    name: 'Max Connection Time'
                    code: conn_limit
                    value: '180'
                    type: numeric
                properties:
                  message:
                    type: string
                    example: 'Operation done successfully.'
                  data:
                    type: object
                    properties:
                      name:
                        type: string
                        example: 'Max Connection Time'
                      code:
                        type: string
                        example: conn_limit
                      value:
                        type: string
                        example: '180'
                      type:
                        type: string
                        example: numeric
      tags:
        - Endpoints
    parameters:
      -
        in: path
        name: code
        description: 'The Code of the Setting.'
        example: conn_limit
        required: true
        schema:
          type: string
  /client/logout:
    post:
      summary: ''
      operationId: postClientLogout
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
  /client/reset-password:
    post:
      summary: ''
      operationId: postClientResetPassword
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
              required:
                - password
  /client/delete-my-account:
    post:
      summary: ''
      operationId: postClientDeleteMyAccount
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                password:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
              required:
                - password
  /client/change-my-password:
    post:
      summary: ''
      operationId: postClientChangeMyPassword
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                old_password:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
                password:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
              required:
                - old_password
                - password
  /client/detect-country:
    post:
      summary: ''
      operationId: postClientDetectCountry
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
  /client/protocols:
    get:
      summary: ''
      operationId: getClientProtocols
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                  data: null
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
                  data:
                    type: string
                    example: null
      tags:
        - Endpoints
  '/client/protocols/{code}':
    get:
      summary: ''
      operationId: getClientProtocolsCode
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                  data: null
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
                  data:
                    type: string
                    example: null
      tags:
        - Endpoints
    parameters:
      -
        in: path
        name: code
        description: 'The code of the Setting.'
        example: 'wireguard, openvpn_udp, openvpn_tcp'
        required: true
        schema:
          type: string
  /client/countries:
    get:
      summary: ''
      operationId: getClientCountries
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                  data: null
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
                  data:
                    type: string
                    example: null
      tags:
        - Endpoints
  '/client/location-protocols/{location_id}':
    get:
      summary: ''
      operationId: getClientLocationProtocolsLocation_id
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                  data: null
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
                  data:
                    type: string
                    example: null
      tags:
        - Endpoints
    parameters:
      -
        in: path
        name: location_id
        description: 'The ID of the location.'
        example: 1
        required: true
        schema:
          type: integer
  /client/servers-to-connect:
    post:
      summary: ''
      operationId: postClientServersToConnect
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                protocol_code:
                  type: string
                  description: 'The <code>code</code> of an existing record in the protocols table.'
                  example: consequatur
                  nullable: true
                location_code:
                  type: string
                  description: 'The <code>code</code> of an existing record in the locations table.'
                  example: consequatur
                  nullable: true
  /client/packages:
    get:
      summary: ''
      operationId: getClientPackages
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                  data: null
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
                  data:
                    type: string
                    example: null
      tags:
        - Endpoints
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  description: 'The page number.'
                  example: 1
                  nullable: true
                limit:
                  type: integer
                  description: 'The number of items per page.'
                  example: 10
                  nullable: true
  /client/package-subscribe:
    post:
      summary: ''
      operationId: postClientPackageSubscribe
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                package_id:
                  type: integer
                  description: 'Must be at least 1.'
                  example: 73
                  nullable: false
              required:
                - package_id
  /client/ticket:
    post:
      summary: ''
      operationId: postClientTicket
      description: ''
      parameters: []
      responses: {  }
      tags:
        - Endpoints
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                email:
                  type: string
                  description: 'Must be a valid email address.'
                  example: <EMAIL>
                  nullable: false
                subject:
                  type: string
                  description: 'Must not be greater than 50 characters.'
                  example: opfuudtdsufvyvddqamni
                  nullable: false
                content:
                  type: string
                  description: ''
                  example: consequatur
                  nullable: false
              required:
                - email
                - subject
                - content
  /client/client:
    get:
      summary: ''
      operationId: getClientClient
      description: ''
      parameters: []
      responses:
        401:
          description: ''
          content:
            application/json:
              schema:
                type: object
                example:
                  message: Unauthenticated.
                  data: null
                properties:
                  message:
                    type: string
                    example: Unauthenticated.
                  data:
                    type: string
                    example: null
      tags:
        - Endpoints
