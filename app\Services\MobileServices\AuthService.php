<?php

namespace App\Services\MobileServices;

use App\Enums\ClientStatusEnum;
use App\Enums\ClientTypesEnum;
use App\Helpers\MailHelper;
use App\Http\Requests\MobileRequests\DeleteMyAccountRequest;
use App\Http\Requests\MobileRequests\LoginRequest;
use App\Http\Requests\MobileRequests\RegisterRequest;
use App\Http\Requests\MobileRequests\ResetPasswordRequest;
use App\Http\Requests\MobileRequests\ValidateResetCodeAndLoginRequest;
use App\Http\Requests\MobileRequests\VerifyEmailRequest;
use App\Mail\PasswordResetMail;
use App\Mail\VerificationCodeMail;
use App\Models\Client;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class AuthService
{
    public function login(LoginRequest $request)
    {
        DB::beginTransaction();
        try {
            $client = Client::whereEmail($request->validated('email'))
                ->whereStatus(ClientStatusEnum::ACTIVE->value)
                ->first();
            if ($client && Hash::check($request->validated('password'), $client['password'])) {
                /* Device Registration */
                $device = $client->registerDevice($request->validated('device_id'));

                $res = $client->tokenize($device['id']);
                DB::commit();
                return $res;
            }
            throw new AuthenticationException('invalid_email_or_password');
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function sendVerificationCode(Client $client)
    {
        $verificationCode = random_int(100000, 999999);
        if (MailHelper::queue($client['email'], new VerificationCodeMail(($verificationCode))))
            return $client->update(["verification_code" => $verificationCode]);
        return false;
    }

    public function register(RegisterRequest $request)
    {
        DB::beginTransaction();
        try {
            /* Client Creation */
            $input = [
                'email' => $request->validated('email'),
                'password' => $request->validated('password'),
                'type' => ClientTypesEnum::FREE->value
            ];
            $client = Client::updateOrCreate(['email' => $input['email']], $input);
            $client->update(['username' => $client->id . Str::random(3)]);

            /* Send code to verfiy the email */
            $this->sendVerificationCode($client);

            DB::commit();
            return $client;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function verifyEmail(VerifyEmailRequest $request, Client $client)
    {
        DB::beginTransaction();
        try {
            if ($client->isVerified()) {
                throw ValidationException::withMessages(['email' => __('messages.your_email_has_been_verified_before')]);
            }
            if (!$client->matchVerificationCode($request->validated('verification_code'))) {
                throw ValidationException::withMessages(['verification_code' => __('messages.invalid_verification_code')]);
            }
            $client->markAsVerified();

            /* Device Registration */
            $device = $client->registerDevice($request->validated('device_id'));

            $res = $client->tokenize($device['id']);
            DB::commit();
            return $res;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function sendResetPasswordCode(Client $client)
    {
        /* Send code to reset the email */
        $verificationCode = random_int(100000, 999999);
        if (MailHelper::queue($client['email'], new PasswordResetMail($verificationCode)))
            DB::table('password_reset_codes')
                ->where('email', $client['email'])
                ->updateOrInsert(['email' => $client['email']], ['code' => $verificationCode]);

        return $client;
    }

    public function validateResetCodeAndLogin(ValidateResetCodeAndLoginRequest $request, Client $client)
    {
        DB::beginTransaction();
        try {
            if (!$client->matchResetPasswordVerificationCode($request->validated('verification_code'))) {
                throw ValidationException::withMessages(['verification_code' => __('messages.invalid_verification_code')]);
            }

            /* Device Registration */
            $device = $client->registerDevice($request->validated('device_id'));

            /* login */
            $res = $client->tokenize($device['id']);

            DB::commit();
            return $res;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function resetPassword(ResetPasswordRequest $request, Client $client)
    {
        DB::beginTransaction();
        try {
            $client->changePassword($request->validated('password'));
            DB::table('password_reset_codes')
                ->where('email', $client['email'])
                ->delete();
            DB::commit();
            return true;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function deleteMyAccount(DeleteMyAccountRequest $request, Client $client)
    {
        if (!$client->matchWithCurrentPassword($request->validated('password')))
            throw ValidationException::withMessages(['password' => __('messages.invalid_password')]);

        $client->delete();
        return true;
    }

    public function changePassword(string $oldPassword, string $newPassword, Client $client)
    {
        if (!$client->matchWithCurrentPassword($oldPassword))
            throw ValidationException::withMessages(['old_password' => __('messages.invalid_password')]);
        if($client->matchWithCurrentPassword($newPassword))
            throw ValidationException::withMessages(['new_password' => __('messages.new_password_must_be_different_from_old_password')]); 

        $client->changePassword($newPassword);
        return true;
    }

    public function createGuest($request)
    {
        DB::beginTransaction();
        try {
            $client = Client::create(['type' => ClientTypesEnum::GUEST->value]);
            $client->update(['username' => $client->id . Str::random(3)]);
            $device = array_merge($request->validated(), ['client_id' => $client->id]);
            $device = $client->devices()->create($device);
            DB::commit();
            return $client;
        } catch (\Throwable $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function getGuest($request)
    {
        $client = Client::whereHas('devices', function ($query) use ($request) {
            $query->where('device_id', $request->device_id);
        })->guests()->first();
        if (!$client) {
            $client = $this->createGuest($request);
        }
        return $client;
    }

    public function loginAsGuest($request)
    {
        $client = $this->getGuest($request);
        $device = $client->registerDevice($request->validated('device_id'));
        $res = $client->tokenize($device->id);
        return $res;
    }
}
