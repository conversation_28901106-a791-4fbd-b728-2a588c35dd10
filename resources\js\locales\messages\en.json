{"header": {"logout": "Logout"}, "locale": {"en": "English", "ar": "Arabic", "lang": "en"}, "subscriptions": {"subscriptions": "Subscriptions", "user_name": "username", "email": "Email", "from": "From", "to": "To", "status": "Status", "is_paid": "<PERSON>", "add": "Add", "package": "Package"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "users": "Users", "users_roles": "Users and Roles", "servers": "Servers", "settings": "Settings", "contents": "Content", "subscription_types": "Subscription Types", "general_codes": "Indexes", "real-estate-types": "Real Estate Types", "locations": "Locations", "roles": "Roles", "powered_by": "مُشغَّل من قبل", "tatweer": "تَطوير", "clients_management": "Clients Management", "clients": "Clients", "services_management": "Services Management", "protocols": "Protocols", "providers_management": "Providers Management", "providers": "Providers", "expenses_management": "Expenses Management", "expenses": "Expenses", "content_management": "Content Management", "groups": "Groups", "tickets": "Tickets", "packages": "Packages", "notifications": "Notifications", "statistics": "Statistics", "clients_statistics": "Clients Statistics", "reports": "Reports", "servers_reports": "Servers Reports", "alerts_reports": "Alerts Reports", "alerts": "<PERSON><PERSON><PERSON>", "infrastructure": "Infrastructure"}, "dashboard": {"subscriptions": {"title": "Subscriptions", "subscriptions_this_month": "Subscriptions This Month", "subscriptions_last_month": "Subscriptions Last Month", "subscriptions_before_last_month": "Subscriptions Before Last Month", "number_of_users_per_package": "Number Of Users Per Package"}, "expenses": {"title": "Expenses", "total": "Total Expenses", "total_this_month": "Expenses This Month", "total_last_month": "Expenses Last Month"}, "notifications": {"title": "Notifications", "sending": "Sending Notifications", "sent": "Sent Notifications"}, "servers": {"title": "Servers", "total_server": "Total Server", "active_server": "Active Server", "free_servers": "Free Servers", "premium_server": "Premium Server"}, "clients": {"title": "Clients"}}, "groups": {"groups": "Groups", "details": "Group details", "edit": "Edit group", "add": "Add a new group", "clients": "Clients", "select_all_clients": "Select all clients", "select_clients": "Select clients", "name": "Name"}, "expenses": {"expenses": "Expenses", "details": "Expense details", "edit": "Edit expense", "add": "Add a new expense", "server_id": "Server", "server": "Server", "amount": "Amount ($)", "description": "Description", "type": "Type", "payment_date": "Payment date"}, "clients": {"clients": "Clients", "client": "Client", "details": "Client Details", "edit": "Edit client", "add": "Add a new client", "username": "Username", "country": "Country", "email": "Email", "password": "Password", "type": "Client type", "status": "Client status", "is_verified": "Is verified", "last_connection": "Last Connection", "id": "ID", "devices": "Devices", "subscriptions": "Subscriptions", "payments": "Payments", "sessions": "Sessions", "registered_at": "Registered at"}, "devices": {"devices": "Devices", "device_id": "Device ID", "device_os": "Device OS", "device_os_version": "Device OS Version", "app_version": "App Version", "fcm_token": "FCM Token", "last_connection_at": "Last Connection At"}, "sessions": {"sessions": "Sessions", "name": "Name", "token": "Token", "secret_key": "Secret Key", "country": "Country", "device_os": "Device OS", "device_id": "Device ID", "last_used_at": "Last Used At"}, "users": {"users": "Users", "details": "User details", "edit": "Edit user", "add": "Add a new user", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "password_confirmation": "Password confirmation", "please_select_roles": "Please select roles", "is_active": "Active"}, "ports": {"ports": "Ports", "details": "Port details", "edit": "Edit port", "add": "Add a new port", "server": "Server", "port": "Port", "protocol": "Protocol", "status": "Status", "control_ip_port": "Control IP Port", "ip": "IP Address", "connections_threshold": "Connections Threshold", "current_connections_count": "Current Connections", "purpose": "Purpose", "server_public_key": "Server Public Key"}, "servers": {"servers": "Servers", "details": "Server details", "edit": "Edit server", "add": "Add a new server", "location": "Location", "country": "Country", "city": "City", "provider": "Provider", "name": "Name", "ip": "IP Address", "socket_port": "Socket Port", "internal_status": "Internal Status", "external_status": "External Status", "health_status": "Health Status", "subscription_cost": "Subscription Cost", "is_free": "Is Free", "ram": "RAM", "cpu": "CPU", "disk": "Disk", "ram_threshold": "RAM Threshold", "cpu_threshold": "CPU Threshold", "disk_threshold": "Disk Threshold", "ram_consumption": "RAM Consumption", "cpu_consumption": "CPU Consumption", "disk_consumption": "Disk Consumption", "change_default_thresholds": "Change Default <PERSON><PERSON><PERSON><PERSON>", "resources": "Resources", "resource": "Resource", "value": "Value", "consumption": "Consumption", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "token": "Token", "regenerate_token": "Regenerate Token", "ram_required": "RAM value is required", "ram_number": "RAM must be a number", "ram_min": "RAM must be at least 1 GB", "ram_max": "RAM cannot exceed 2048 GB (2 TB)", "ram_standard": "RAM should be in standard sizes (2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048 GB)", "ram_hint": "Enter RAM size in GB (2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048)", "cpu_required": "CPU value is required", "cpu_number": "CPU must be a number", "cpu_min": "CPU must be at least 1 core", "cpu_max": "CPU cannot exceed 128 cores", "cpu_standard": "CPU cores should be in standard counts (1, 2, 4, 6, 8, 12, 16, 24, 32, 48, 64, 96, 128)", "cpu_hint": "Enter CPU cores (1, 2, 4, 6, 8, 12, 16, 24, 32, 48, 64, 96, 128)", "disk_required": "Disk value is required", "disk_number": "Disk must be a number", "disk_min": "Disk must be at least 10 GB", "disk_max": "Disk cannot exceed 8000 GB (8 TB)", "disk_standard": "Disk size should be in standard sizes (10, 20, 40, 50, 100, 200, 250, 500, 1000, 2000, 4000, 8000 GB)", "disk_hint": "Enter disk size in GB (10, 20, 40, 50, 100, 200, 250, 500, 1000, 2000, 4000, 8000)", "threshold_required": "Threshold value is required", "threshold_number": "Threshold must be a number", "threshold_min": "Threshold must be at least 1%", "threshold_max": "Thresh<PERSON> cannot exceed 100%", "threshold_hint": "Enter threshold percentage (1-100)"}, "protocols": {"protocols": "Protocols", "details": "Protocol details", "edit": "Edit protocol", "add": "Add a new protocol", "name": "Name", "code": "Code", "version": "Version", "form_note": "Use {'{{variable_name}}'} format to define placeholders that will be replaced by the mobile application ( example : Endpoint = {'{{ip}}:{{port}}'})", "template": "Template"}, "roles": {"roles": "Roles", "details": "Role details", "edit": "Edit role", "add": "Add a new role", "please_select_permissions": "Please select permissions", "roles_selected": "Roles selected"}, "permissions": {"permissions": "Permissions", "permissions_selected": "Permissions selected"}, "validation": {"required": "Required field", "email": "The field must be a valid email", "password": "The password must be at least 8 and at most 20 characters and should include a mix of characters", "phone": "The phone number must be 10 numbers starting with 0", "optional": "It is required not to enter empty spaces or start with an empty space.", "beforeOrEqual": "The date must be before or equal to {date}.", "before": "The date must be before {date}.", "afterOrEqual": "The date must be after or equal to {date}.", "after": "The date must be after {date}.", "minLength": "This field must be at least {length} characters long.", "maxLength": "This field must not exceed {length} characters.", "minValue": "Value must be at least {min}.", "maxValue": "Value must not exceed {max}.", "alpha": "Only alphabetic characters are allowed.", "alphaNum": "Only alphanumeric characters are allowed without spaces.", "numeric": "Only numeric values are allowed.", "integer": "Only integer values are allowed.", "decimal": "Only decimal values are allowed.", "between": "Value must be between {min} and {max}.", "url": "Invalid URL.", "sameAs": "This field must match the required value.", "requiredIf": "This field is required {message}.", "requiredUnless": "This field is required {message}.", "contains": "The value must contain '{string}'.", "startsWith": "The value must start with '{string}'.", "endsWith": "The value must end with '{string}'.", "ipAddress": "Invalid IP address.", "ipPort": "Invalid IP:Port.", "mobile": "The mobile number must be 10 numbers starting with 09"}, "is_active": {"is_active": "Display on application", "1": "Yes", "0": "No"}, "locations": {"locations": "Locations", "details": "Details", "edit": "Edit", "add": "Add", "code": "Code", "auto_location": "Auto Location", "auto_protocol": "Auto Protocol", "free_servers_count": "Free Servers Count", "premium_servers_count": "Premium Servers Count"}, "settings": {"settings": "Settings", "details": "Details", "edit": "Edit", "name": "Name", "value": "Value", "type": "Type", "contents": "Content"}, "providers": {"providers": "Providers", "details": "Details", "edit": "Edit", "add": "Add", "name": "Name", "code": "Code", "contact_number": "Contact Number", "email": "Email", "website": "Website", "admin_url": "Admin URL", "note": "Note"}, "notifications": {"notifications": "Notifications", "details": "Details", "edit": "Edit", "add": "Add", "subject": "Subject", "content": "Content", "type": "Type", "group": "Group", "scheduled_at": "Scheduled Date", "status": "Status", "clients": "Clients", "user_name": "Username", "sent_date": "Sent Date"}, "packages": {"packages": "packages", "details": "Details", "edit": "Edit", "add": "Add", "name": "Name", "duration": "Duration", "unit": "Unit", "status": "Status", "price": "Price ($)", "subscriptions": "Subscriptions", "payments": "Payments"}, "login": "<PERSON><PERSON>", "save": "Save", "Yes": "Yes", "No": "No", "actions": "Actions", "next": "Next", "add": "Add", "back": "Back", "left": "left", "right": "right", "edit": "Edit", "delete": "Delete", "name": "Name", "description": "Description", "name_en": "English name", "name_ar": "Arabic name", "description_en": "English description", "description_ar": "Arabic description", "amount": "Subscription fee", "please_select": "Please Select", "cancel": "Cancel", "confirm": "Confirm", "are_you_sure": "Are you sure?", "code": "Code", "parent_id": "Parent", "level": "Level", "accept": "Accept", "reject": "Reject", "pay": "Pay", "define_about_confirm_delete": "Note that it can't be reversed later", "Passport": "Passport", "PersonalID": "Personal ID", "unauthorized": "You do not have permission for that", "select": "Select", "yes": "Yes", "no": "No", "export_to_excel": "Export to Excel", "tickets": {"tickets": "Tickets", "details": "Details", "Handle": "<PERSON>le Ticket", "add": "Add", "username": "UserName", "email": "Email", "submitted_at": "Submitted At", "status": "Status", "notes_at": "Notes At", "subject": "Subject", "note": "Note", "content": "Content"}, "reports": {"reports": "Reports", "servers": {"servers": "Servers", "name": "Name", "status": "Status", "cpu": "CPU%", "ram": "RAM%", "disk": "Disk%", "tcp_users": "TCP Users", "udp_users": "Udp Users", "wireguard_users": "WireGuard Users", "total_users": "Total Users"}, "alerts": {"alerts": "<PERSON><PERSON><PERSON>", "recipient_email": "Recipient Email", "subject": "Alert Subject", "status": "Alert <PERSON>", "error_message": "Error Message", "sent_at": "<PERSON><PERSON>"}}, "boolean_type": {"1": "True", "0": "False"}, "statistics": {"total_users": "Total Clients", "logged_in_clients": "Logged in Clients", "free_users": "Free Clients", "paid_users": "Paid <PERSON>s", "logged_in_users": "Logged in Clients", "today": "Today", "last_7_days": "Last 7 Days", "last_30_days": "Last 30 Days", "new_users_and_conversion": "New Clients & Conversion", "new_users_this_month": "New Clients This Month", "free_to_paid_conversion": "Free to Paid Conversion Rate", "user_distribution_by_country": "Client Distribution by Country", "country": "Country", "users": "Clients", "no_data": "No data available", "period": "Period", "metric": "Metric", "value": "Value"}}