{"variable": [{"id": "baseUrl", "key": "baseUrl", "type": "string", "name": "string", "value": "http://captainvpn.test"}], "info": {"name": "Laravel-vue-base API Documentation", "_postman_id": "473fc5d6-48c0-4cd1-84f6-42365a28e698", "description": "", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Endpoints", "description": "", "item": [{"name": "POST client/login", "request": {"url": {"host": "{{baseUrl}}", "path": "client/login", "query": [], "raw": "{{baseUrl}}/client/login"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"device_id\":\"vmqeopfuudtdsufvyvddq\",\"email\":\"<EMAIL>\",\"password\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "POST client/login-as-guest", "request": {"url": {"host": "{{baseUrl}}", "path": "client/login-as-guest", "query": [], "raw": "{{baseUrl}}/client/login-as-guest"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"device_id\":\"vmqeopfuudtdsufvyvddq\"}"}, "description": ""}, "response": []}, {"name": "POST client/register", "request": {"url": {"host": "{{baseUrl}}", "path": "client/register", "query": [], "raw": "{{baseUrl}}/client/register"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\":\"<EMAIL>\",\"password\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "POST client/resend-verification-code", "request": {"url": {"host": "{{baseUrl}}", "path": "client/resend-verification-code", "query": [], "raw": "{{baseUrl}}/client/resend-verification-code"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\":\"<EMAIL>\"}"}, "description": ""}, "response": []}, {"name": "POST client/verify-email", "request": {"url": {"host": "{{baseUrl}}", "path": "client/verify-email", "query": [], "raw": "{{baseUrl}}/client/verify-email"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"device_id\":\"vmqeopfuudtdsufvyvddq\",\"email\":\"<EMAIL>\",\"verification_code\":\"810798\"}"}, "description": ""}, "response": []}, {"name": "POST client/send-reset-password-code", "request": {"url": {"host": "{{baseUrl}}", "path": "client/send-reset-password-code", "query": [], "raw": "{{baseUrl}}/client/send-reset-password-code"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\":\"<EMAIL>\"}"}, "description": ""}, "response": []}, {"name": "POST client/verify-reset-code-login", "request": {"url": {"host": "{{baseUrl}}", "path": "client/verify-reset-code-login", "query": [], "raw": "{{baseUrl}}/client/verify-reset-code-login"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"device_id\":\"vmqeopfuudtdsufvyvddq\",\"email\":\"<EMAIL>\",\"verification_code\":\"810798\"}"}, "description": ""}, "response": []}, {"name": "POST client/social-login", "request": {"url": {"host": "{{baseUrl}}", "path": "client/social-login", "query": [], "raw": "{{baseUrl}}/client/social-login"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"token\":\"consequatur\",\"provider\":\"apple\",\"device_id\":\"mqeopfuudtdsufvyvddqa\"}"}, "description": ""}, "response": []}, {"name": "GET client/settings", "request": {"url": {"host": "{{baseUrl}}", "path": "client/settings", "query": [], "raw": "{{baseUrl}}/client/settings"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 200, "body": "{\"message\":\"Operation done successfully.\",\"data\":[{\"name\":\"Email Address\",\"code\":\"email\",\"value\":\"<EMAIL>\",\"type\":\"email\"},{\"name\":\"Max Connection Time\",\"code\":\"conn_limit\",\"value\":\"180\",\"type\":\"numeric\"},{\"name\":\"Ads Activation\",\"code\":\"ads_act\",\"value\":\"0\",\"type\":\"bool\"},{\"name\":\"Upgrade To Premium\",\"code\":\"premium\",\"value\":\"0\",\"type\":\"bool\"},{\"name\":\"Server RAM threshold (%)\",\"code\":\"serv_ram_t\",\"value\":\"80\",\"type\":\"numeric\"},{\"name\":\"Server CPU threshold (%)\",\"code\":\"serv_cpu_t\",\"value\":\"80\",\"type\":\"numeric\"},{\"name\":\"Server DISK threshold (%)\",\"code\":\"serv_dis_t\",\"value\":\"80\",\"type\":\"numeric\"},{\"name\":\"Phone1\",\"code\":\"pho1\",\"value\":\"+971-4354-332\",\"type\":\"text\"},{\"name\":\"facebook\",\"code\":\"acc_fa\",\"value\":\"https:\\/\\/www.facebook.com\\/CaptainVpn\",\"type\":\"link\"},{\"name\":\"instagram\",\"code\":\"acc_in\",\"value\":\"https:\\/\\/www.instagram.com\\/CaptainVpn\",\"type\":\"link\"},{\"name\":\"X\",\"code\":\"acc_x\",\"value\":\"https:\\/\\/www.x.com\\/CaptainVpn\",\"type\":\"link\"},{\"name\":\"Youtube\",\"code\":\"acc_yo\",\"value\":\"https:\\/\\/www.Youtube.com\\/CaptainVpn\",\"type\":\"link\"},{\"name\":\"About Us\",\"code\":\"abo_us\",\"value\":\"Captain Vpn is ....\",\"type\":\"text\"},{\"name\":\"privacy policy\",\"code\":\"pr_po\",\"value\":\"We are committed to maintaining the accuracy, confidentiality, and security of your personally identifiable information (\\\"Personal Information\\\")....\",\"type\":\"text\"},{\"name\":\"Terms of Service\",\"code\":\"ter_s\",\"value\":\"<p><strong>Terms of Service<\\/strong><\\/p><p><em>Last Updated: April 22, 2025<\\/em><\\/p><p><br><\\/p><p>These Terms of Service (\\u201cTerms\\u201d), along with the Privacy Policy, govern your use of the services, website, applications, content, and software (collectively, the \\u201cServices\\u201d) offered by CaptainVPN (\\u201cCaptainVPN,\\u201d \\u201cwe,\\u201d \\u201cus,\\u201d or \\u201cour\\u201d), a company incorporated in the United States.<\\/p><p>By using the Services, you agree to these Terms and the Privacy Policy (together, the \\u201cAgreement\\u201d). This Agreement is a legally binding contract between you and CaptainVPN. If you disagree with any part of this Agreement, you may not use the Services.<\\/p><p><br><\\/p><h3>Acceptance<\\/h3><p>By accessing or using the Services, you represent that:<\\/p><ul><li>You are at least 18 years old (or the age of majority in your jurisdiction);<\\/li><li>You have read, understood, and agree to be bound by this Agreement;<\\/li><li>All registration information you provide is accurate and complete.<\\/li><\\/ul><p>If you accept these Terms on behalf of an employer or entity, you warrant that you have full legal authority to bind them to this Agreement.<\\/p><p><br><\\/p><h3>Modification<\\/h3><p>CaptainVPN may update these Terms at any time. Material changes (e.g., pricing, dispute resolution, or data practices) will be communicated via email, Account notifications, or Site banners. Continued use after changes take effect constitutes acceptance.<\\/p><p><br><\\/p><h3>Privacy Policy<\\/h3><p>CaptainVPN does not log browsing history, traffic destination, data content, DNS queries, or user-linked IP addresses, except as necessary for billing, troubleshooting, or legal compliance. For details, review our Privacy Policy, which complies with U.S. laws (e.g., CCPA) and international regulations (e.g., GDPR for EU users).<\\/p><p><br><\\/p><h3>Subscriptions<\\/h3><ul><li>Subscription fees and plans are listed on the Site. CaptainVPN may change fees with 30 days' notice (effective upon renewal).<\\/li><li><strong>Auto-renewal<\\/strong>: Enabled by default unless canceled via your Account or by emailing <a href=\\\"mailto:<EMAIL>\\\" rel=\\\"noopener noreferrer\\\" target=\\\"_blank\\\"><EMAIL><\\/a>. California users: You may cancel auto-renewal online at least 30 days before renewal.<\\/li><li>You are solely responsible for Account security and activity.<\\/li><li><br><\\/li><\\/ul><h3>Refund Policy<\\/h3><ul><li><strong>Money-Back Guarantee<\\/strong>: Full refunds within 3 days of purchase.<\\/li><li>Post-3-day refunds require proof of Service unavailability and are issued at CaptainVPN\\u2019s discretion.<\\/li><li>Refunds are processed in USD; amounts may vary due to exchange rates or third-party fees.<\\/li><li>In-app purchases: Refunds via Apple\\/Google stores only.<\\/li><li><br><\\/li><\\/ul><h3>Acceptable Use Policy<\\/h3><p>You agree not to:<\\/p><ul><li>Engage in illegal activities (fraud, hacking, terrorism, phishing) under U.S. federal or state laws.<\\/li><li>Transmit viruses, malware, or spam.<\\/li><li>Share copyrighted materials (see DMCA Compliance below) or child exploitation content.<\\/li><li>Harass others or conduct DDoS attacks\\/unauthorized scanning.<\\/li><li>Use unauthorized payment methods.<\\/li><\\/ul><p>Violations may result in immediate termination without refund.<\\/p><p><br><\\/p><h3>Third-Party Content<\\/h3><p>CaptainVPN is not responsible for third-party websites, content, or services linked through the Services. Use at your own risk.<\\/p><p><br><\\/p><h3>Disclaimers<\\/h3><p>Services are provided \\u201cas-is.\\u201d CaptainVPN disclaims all warranties (express\\/implied), including merchantability or fitness for a particular purpose. Service availability, speed, and coverage may vary.<\\/p><p><br><\\/p><h3>Limitations of Liability<\\/h3><p>CaptainVPN is not liable for:<\\/p><ul><li>Service interruptions;<\\/li><li>Third-party actions;<\\/li><li>Unauthorized Account access;<\\/li><li>Indirect\\/consequential damages.<\\/li><\\/ul><p>Exclusions do not apply to liability under California law or federal statutes (e.g., death, personal injury, fraud).<\\/p><p><br><\\/p><h3>Indemnification<\\/h3><p>You agree to indemnify CaptainVPN for claims arising from your breach of this Agreement or misuse of the Services, including reasonable attorneys' fees.<\\/p><p><br><\\/p><h3>Termination<\\/h3><ul><li><strong>By You<\\/strong>: Cancel via Account dashboard (no prorated refunds).<\\/li><li><strong>By CaptainVPN<\\/strong>: Immediate termination for violations, with no refund.<\\/li><\\/ul><p><br><\\/p><h3>Dispute Resolution &amp; Governing Law<\\/h3><ul><li><strong>Governing Law<\\/strong>: This Agreement is governed by the laws of the State of Delaware without regard to conflict-of-law principles.<\\/li><li><strong>Arbitration<\\/strong>: Any dispute will be resolved by binding arbitration under the AAA Commercial Rules. Class actions are waived.<\\/li><\\/ul><p><br><\\/p><h3>General<\\/h3><ul><li><strong>Force Majeure<\\/strong>: CaptainVPN is not liable for delays beyond its control (e.g., natural disasters, government actions).<\\/li><li><strong>Assignment<\\/strong>: CaptainVPN may transfer this Agreement to a successor entity.<\\/li><li><strong>Severability<\\/strong>: Invalid clauses will be amended to reflect intent; remainder of Agreement remains valid.<\\/li><\\/ul><p><br><\\/p><p><strong>Contact Us:<\\/strong><\\/p><p>For questions, contact <a href=\\\"mailto:<EMAIL>\\\" rel=\\\"noopener noreferrer\\\" target=\\\"_blank\\\"><EMAIL><\\/a>.<\\/p>\",\"type\":\"text\"},{\"name\":\"Video Ad Activation\",\"code\":\"vid_ad_act\",\"value\":\"0\",\"type\":\"bool\"},{\"name\":\"Main Load Ad Activation\",\"code\":\"ml_ad_act\",\"value\":\"0\",\"type\":\"bool\"},{\"name\":\"All Ads Activation\",\"code\":\"all_ad_act\",\"value\":\"0\",\"type\":\"bool\"},{\"name\":\"Golden Hour Ad Period\",\"code\":\"gh_ad_per\",\"value\":\"60\",\"type\":\"numeric\"},{\"name\":\"One Golden Hour Activation\",\"code\":\"one_gh_act\",\"value\":\"0\",\"type\":\"bool\"},{\"name\":\"Protocol Input Name\",\"code\":\"prot_name\",\"value\":\"select mode\",\"type\":\"text\"},{\"name\":\"Server Input Name\",\"code\":\"serv_name\",\"value\":\"select country\",\"type\":\"text\"},{\"name\":\"Banner Ad Activation\",\"code\":\"ban_ad_act\",\"value\":\"0\",\"type\":\"bool\"}]}", "name": ""}]}, {"name": "GET client/settings/{code}", "request": {"url": {"host": "{{baseUrl}}", "path": "client/settings/:code", "query": [], "raw": "{{baseUrl}}/client/settings/:code", "variable": [{"id": "code", "key": "code", "value": "conn_limit", "description": "The Code of the Setting."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 200, "body": "{\"message\":\"Operation done successfully.\",\"data\":{\"name\":\"Max Connection Time\",\"code\":\"conn_limit\",\"value\":\"180\",\"type\":\"numeric\"}}", "name": ""}]}, {"name": "POST client/logout", "request": {"url": {"host": "{{baseUrl}}", "path": "client/logout", "query": [], "raw": "{{baseUrl}}/client/logout"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": []}, {"name": "POST client/reset-password", "request": {"url": {"host": "{{baseUrl}}", "path": "client/reset-password", "query": [], "raw": "{{baseUrl}}/client/reset-password"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"password\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "POST client/delete-my-account", "request": {"url": {"host": "{{baseUrl}}", "path": "client/delete-my-account", "query": [], "raw": "{{baseUrl}}/client/delete-my-account"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"password\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "POST client/change-my-password", "request": {"url": {"host": "{{baseUrl}}", "path": "client/change-my-password", "query": [], "raw": "{{baseUrl}}/client/change-my-password"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"old_password\":\"consequatur\",\"password\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "POST client/detect-country", "request": {"url": {"host": "{{baseUrl}}", "path": "client/detect-country", "query": [], "raw": "{{baseUrl}}/client/detect-country"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": []}, {"name": "GET client/protocols", "request": {"url": {"host": "{{baseUrl}}", "path": "client/protocols", "query": [], "raw": "{{baseUrl}}/client/protocols"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 401, "body": "{\"message\":\"Unauthenticated.\",\"data\":null}", "name": ""}]}, {"name": "GET client/protocols/{code}", "request": {"url": {"host": "{{baseUrl}}", "path": "client/protocols/:code", "query": [], "raw": "{{baseUrl}}/client/protocols/:code", "variable": [{"id": "code", "key": "code", "value": "wireguard%2C+openvpn_udp%2C+openvpn_tcp", "description": "The code of the Setting."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 401, "body": "{\"message\":\"Unauthenticated.\",\"data\":null}", "name": ""}]}, {"name": "GET client/countries", "request": {"url": {"host": "{{baseUrl}}", "path": "client/countries", "query": [], "raw": "{{baseUrl}}/client/countries"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 401, "body": "{\"message\":\"Unauthenticated.\",\"data\":null}", "name": ""}]}, {"name": "GET client/location-protocols/{location_id}", "request": {"url": {"host": "{{baseUrl}}", "path": "client/location-protocols/:location_id", "query": [], "raw": "{{baseUrl}}/client/location-protocols/:location_id", "variable": [{"id": "location_id", "key": "location_id", "value": "1", "description": "The ID of the location."}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 401, "body": "{\"message\":\"Unauthenticated.\",\"data\":null}", "name": ""}]}, {"name": "POST client/servers-to-connect", "request": {"url": {"host": "{{baseUrl}}", "path": "client/servers-to-connect", "query": [], "raw": "{{baseUrl}}/client/servers-to-connect"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"protocol_code\":\"consequatur\",\"location_code\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "GET client/packages", "request": {"url": {"host": "{{baseUrl}}", "path": "client/packages", "query": [], "raw": "{{baseUrl}}/client/packages"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"page\":1,\"limit\":10}"}, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 401, "body": "{\"message\":\"Unauthenticated.\",\"data\":null}", "name": ""}]}, {"name": "POST client/package-subscribe", "request": {"url": {"host": "{{baseUrl}}", "path": "client/package-subscribe", "query": [], "raw": "{{baseUrl}}/client/package-subscribe"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"package_id\":73}"}, "description": ""}, "response": []}, {"name": "POST client/ticket", "request": {"url": {"host": "{{baseUrl}}", "path": "client/ticket", "query": [], "raw": "{{baseUrl}}/client/ticket"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"email\":\"<EMAIL>\",\"subject\":\"opfuudtdsufvyvddqamni\",\"content\":\"consequatur\"}"}, "description": ""}, "response": []}, {"name": "GET client/client", "request": {"url": {"host": "{{baseUrl}}", "path": "client/client", "query": [], "raw": "{{baseUrl}}/client/client"}, "method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": null, "description": ""}, "response": [{"header": [{"key": "cache-control", "value": "no-cache, private"}, {"key": "content-type", "value": "application/json"}], "code": 401, "body": "{\"message\":\"Unauthenticated.\",\"data\":null}", "name": ""}]}]}], "auth": {"type": "bearer", "bearer": [{"key": "Authorization", "type": "string"}]}}